'use client'

import { useEffect, useRef, useState } from 'react'
import useSWRInfinite from 'swr/infinite'
import { useTranslation } from 'react-i18next'
import { useDebounceFn } from 'ahooks'
import CompanyDatasetsCard from './CompanyDatasetsCard'
import { fetchAppList } from '@/service/apps'
import Input from '@/app/components/base/input'
import TabSlider from '@/app/components/base/tab-slider'

type ListResponse = {
  list: any
  has_more: boolean
  page_size: number
  page: number
  total: number
}
const getKey = (
  pageIndex: number,
  previousPageData: ListResponse,
  activeTab: number,
  keywords: string,
) => {
  if (!pageIndex || previousPageData.has_more) {
    const params: any = {
      url: 'enterprise-brain/agents',
      params: {
        page: pageIndex + 1,
        size: 10,
        page_size: 10,
        enterprise_brain_url: 'localhost',
        enterprise_brain_token: 'test',
        type: activeTab || 0,
        name: keywords,
      },
    }
    return params
  }
  return null
}

const CompanyDatasets = ({ selectedKnowledgeId, setSelectedKnowledgeId }: any) => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState<any>(0)
  const [keywords, setKeywords] = useState<any>('')
  const [searchKeywords, setSearchKeywords] = useState(keywords)

  const { data, isLoading, error, setSize, mutate } = useSWRInfinite(
    (pageIndex: number, previousPageData: ListResponse) => getKey(pageIndex, previousPageData, activeTab, searchKeywords),
    fetchAppList,
    {
      revalidateFirstPage: true,
      shouldRetryOnError: false,
      dedupingInterval: 500,
      errorRetryCount: 3,
    },
  )

  const anchorRef = useRef<HTMLDivElement>(null)
  const options = [
    { value: 0, text: '公共知识库' },
    { value: 1, text: '组织知识库' },
    { value: 2, text: '专属知识库' },
  ]

  useEffect(() => {
    mutate()
  }, [mutate, t])

//   useEffect(() => {
//     const hasMore = data?.at(-1)?.has_more ?? true
//     let observer: IntersectionObserver | undefined

//     if (error) {
//       if (observer)
//         observer.disconnect()
//       return
//     }

//     // if (anchorRef.current) {
//     //   observer = new IntersectionObserver((entries) => {
//     //     if (entries[0].isIntersecting && !isLoading && !error && hasMore)
//     //       setSize((size: number) => size + 1)
//     //   }, { rootMargin: '100px' })
//     //   observer.observe(anchorRef.current)
//     // }
//     return () => observer?.disconnect()
//   }, [isLoading, setSize, anchorRef, mutate, data, error])

  const { run: handleSearch } = useDebounceFn(() => {
    setSearchKeywords(keywords)
  }, { wait: 500 })
  const handleKeywordsChange = (value: string) => {
    setKeywords(value)
    handleSearch()
  }

  return (
    <>
      <div ref={anchorRef} className='relative flex shrink-0 grow flex-col overflow-y-auto bg-background-body'>
        <div className='sticky top-0 z-10 flex flex-wrap items-center justify-between gap-y-2 bg-background-body px-4 py-2 leading-[56px]'>
          <TabSlider
            className='mt-1 shrink-0 px-4'
            itemClassName='py-3'
            noBorderBottom
            smallItem
            value={activeTab}
            onChange={setActiveTab}
            options={options}
          />
          <div className='flex items-center gap-2'>
            <Input
              showLeftIcon
              showClearIcon
              wrapperClassName='w-[200px]'
              value={keywords}
              onChange={e => handleKeywordsChange(e.target.value)}
              onClear={() => handleKeywordsChange('')}
            />
          </div>
        </div>
        {(data && data[0].total > 0)
          ? <div className='relative mb-4 grid grow grid-cols-1 content-start gap-4 px-4 py-2 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 2k:grid-cols-6'>
            { data.map((item: any) => item.list.map((app: any) => (
              <CompanyDatasetsCard key={app.id} app={app} selectedKnowledgeId={selectedKnowledgeId} setSelectedKnowledgeId={setSelectedKnowledgeId} />
            )))}
          </div> : <div className='relative mb-4 grid grow grid-cols-1 content-start gap-4 overflow-hidden px-4 py-2 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 2k:grid-cols-6'>
            <NoDataFound />
          </div>
        }
      </div>
      <div className='mb-6 flex px-4 py-2 text-sm  font-semibold leading-5 text-text-secondary'>
        共{ (data && data[0].total) || 0}个
      </div>
    </>
  )
}

export default CompanyDatasets

function NoDataFound() {
//   const { t } = useTranslation()
  function renderDefaultCard() {
    const defaultCards = Array.from({ length: 36 }, (_, index) => (
      <div key={index} className='inline-flex rounded-xl bg-background-default-lighter'></div>
    ))
    return defaultCards
  }
  return (
    <>
      {renderDefaultCard()}
      <div className='absolute bottom-0 left-0 right-0 top-0 flex items-center justify-center bg-gradient-to-t from-background-body to-transparent'>
        <span className='system-md-medium text-text-tertiary'>暂无数据</span>
      </div>
    </>
  )
}
