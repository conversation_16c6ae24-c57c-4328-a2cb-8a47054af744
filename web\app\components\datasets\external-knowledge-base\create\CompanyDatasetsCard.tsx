'use client'

type CardProps = {
  app: any,
  selectedKnowledgeId: string
  setSelectedKnowledgeId: any
}

const CompanyDatasetsCard = ({ app, selectedKnowledgeId, setSelectedKnowledgeId }: CardProps) => {
  return (
    <>
      <div
        onClick={(e) => {
          e.preventDefault()
          setSelectedKnowledgeId(app.id)
        }}
        className='group relative col-span-1 inline-flex cursor-pointer flex-col rounded-xl border-[1px] border-solid border-components-card-border bg-components-card-bg shadow-sm transition-all duration-200 ease-in-out hover:shadow-lg'
      >
        <div className='flex shrink-0 grow-0 items-center gap-3 px-[14px] pb-3 pt-[14px]'>
          <div className='w-0 grow py-[1px]'>
            <div className='flex items-center text-sm font-semibold leading-5 text-text-secondary'>
              <div className='truncate' title={app.name}>{app.name}</div>
            </div>
            <div className='flex items-center gap-1 text-[10px] font-medium leading-[18px] text-text-tertiary'>
              <div className='truncate' title={'共0个知识'}>共0个知识</div>
            </div>
          </div>
        </div>
        <div className='flex h-5 items-center'>
        {/* <Checkbox
          className='shrink-0'
          checked={selectedKnowledgeId === app.id}

        /> */}
        </div>
      </div>
    </>
  )
}

export default CompanyDatasetsCard
