'use client'

import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { RiCloseLine } from '@remixicon/react'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import CompanyDatasets from './CompanyDatasets'
type KnowledgeBaseSelectorModalProps = {
  className?: string
  onClose: () => void
  onSelect: (knowledgeId: string) => void
  selectedApiId?: any
}

const KnowledgeBaseSelectorModal: React.FC<KnowledgeBaseSelectorModalProps> = ({ onClose, onSelect, selectedApiId }) => {
  const { t } = useTranslation()

  const [selectedKnowledgeId, setSelectedKnowledgeId] = useState(selectedApiId)
  const handleSelect = () => {
    if (selectedKnowledgeId)
      onSelect(selectedKnowledgeId)
  }

  return (
    <Modal isShow={true} onClose={onClose} className='relative !max-w-[1080px] px-8'>
      <div className='absolute right-4 top-4 cursor-pointer p-2' onClick={onClose}>
        <RiCloseLine className='h-4 w-4 text-text-tertiary' />
      </div>
      <div className='mb-4 text-xl font-semibold leading-[30px] text-text-primary'>
        企业大脑知识库列表
      </div>
      <CompanyDatasets
        selectedKnowledgeId={selectedKnowledgeId}
        setSelectedKnowledgeId={setSelectedKnowledgeId}
      />
      <div className='flex flex-row-reverse'>
        <Button
          onClick={onClose}
          className="ml-2"
        >
          {t('common.operation.cancel')}
        </Button>
        <Button
          variant="primary"
          onClick={handleSelect}
          disabled={!selectedKnowledgeId}
        >
          {t('common.operation.ok')}
        </Button>
      </div>
    </Modal>
  )
}

export default KnowledgeBaseSelectorModal
