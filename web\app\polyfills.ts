// Polyfills for older browsers
// This file should be imported at the top of your main layout or _app file

// Object.assign polyfill
if (typeof Object.assign !== 'function') {
  Object.assign = function(target: any, ...sources: any[]) {
    if (target == null) {
      throw new TypeError('Cannot convert undefined or null to object')
    }

    const to = Object(target)

    for (let index = 0; index < sources.length; index++) {
      const nextSource = sources[index]

      if (nextSource != null) {
        for (const nextKey in nextSource) {
          if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
            to[nextKey] = nextSource[nextKey]
          }
        }
      }
    }
    return to
  }
}

// Array.from polyfill
if (!Array.from) {
  Array.from = function(arrayLike: any, mapFn?: any, thisArg?: any) {
    const C = this
    const items = Object(arrayLike)
    if (arrayLike == null) {
      throw new TypeError('Array.from requires an array-like object - not null or undefined')
    }
    const mapFunction = mapFn === undefined ? undefined : mapFn
    if (typeof mapFunction !== 'undefined' && typeof mapFunction !== 'function') {
      throw new TypeError('Array.from: when provided, the second argument must be a function')
    }
    const len = parseInt(items.length) || 0
    const A = typeof C === 'function' ? Object(new C(len)) : new Array(len)
    let k = 0
    let kValue
    while (k < len) {
      kValue = items[k]
      if (mapFunction) {
        A[k] = typeof thisArg === 'undefined' ? mapFunction(kValue, k) : mapFunction.call(thisArg, kValue, k)
      } else {
        A[k] = kValue
      }
      k += 1
    }
    A.length = len
    return A
  }
}

// Promise polyfill (basic implementation)
if (typeof Promise === 'undefined') {
  // This is a very basic Promise polyfill
  // For production, consider using a proper polyfill library
  (window as any).Promise = class {
    constructor(executor: any) {
      // Basic Promise implementation
      // This is just a placeholder - use a proper polyfill in production
    }
  }
}

// String.prototype.includes polyfill
if (!String.prototype.includes) {
  String.prototype.includes = function(search: string, start?: number) {
    if (typeof start !== 'number') {
      start = 0
    }
    
    if (start + search.length > this.length) {
      return false
    } else {
      return this.indexOf(search, start) !== -1
    }
  }
}

// Array.prototype.includes polyfill
if (!Array.prototype.includes) {
  Array.prototype.includes = function(searchElement: any, fromIndex?: number) {
    return this.indexOf(searchElement, fromIndex) !== -1
  }
}

// String.prototype.startsWith polyfill
if (!String.prototype.startsWith) {
  String.prototype.startsWith = function(searchString: string, position?: number) {
    position = position || 0
    return this.substr(position, searchString.length) === searchString
  }
}

// String.prototype.endsWith polyfill
if (!String.prototype.endsWith) {
  String.prototype.endsWith = function(searchString: string, length?: number) {
    if (length === undefined || length > this.length) {
      length = this.length
    }
    return this.substring(length - searchString.length, length) === searchString
  }
}

export {}
